package com.example.yin.controller;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.example.yin.common.R;
import com.example.yin.utils.FileUtil;

@RestController
public class FileController {

	/**
	 * 上传文件
	 * @param file
	 * @return
	 */
    @RequestMapping("/v1/file/upload")
    public R upload(MultipartFile file){
        try {
            String originalFilename = file.getOriginalFilename();
            // 生成目标文件
            File destFile = FileUtil.getDestFile(originalFilename);
            // 保存到磁盘
            file.transferTo(destFile);
            // 返回文件路径
            String path = destFile.getAbsolutePath().replace(FileUtil.getProjectFilePath(), "");
            return R.success("上传成功",FileUtil.buildLinuxPath(path));
        }catch (Exception e){
            return R.error("上传失败");
        }
    }

	/**
	 * 下载文件
	 * @param url 文件相对路径
	 * @param request HTTP请求
	 * @param response HTTP响应
	 */
    @GetMapping("/v1/file/download")
    public void download(@RequestParam("url") String url, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 构建文件路径
            File file = new File(FileUtil.adaptPath(FileUtil.getProjectFilePath() + url));

            // 检查文件是否存在
            if (!file.exists() || !file.isFile()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
				response.setContentType(MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8");
                response.getWriter().write("文件不存在");
                return;
            }

            // 获取文件名
            String fileName = file.getName();
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

            // 设置响应头
            response.setContentType(getContentType(fileName));
            response.setContentLengthLong(file.length());
			response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
			response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // 使用缓冲流传输文件
            try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));
                 OutputStream os = response.getOutputStream()) {

                byte[] buffer = new byte[8192]; // 8KB缓冲区
                int bytesRead;
                while ((bytesRead = bis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

        } catch (IOException e) {
            try {
                if (!response.isCommitted()) {
                    response.reset();
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("文件下载失败: " + e.getMessage());
                }
            } catch (IOException ex) {
                // 记录日志或处理异常
            }
        }
    }


	/**
	 * 预览文件
	 * @param url 文件相对路径
	 * @param request HTTP请求
	 * @param response HTTP响应
	 */
	@GetMapping("/v1/file/preview")
	public void preview(@RequestParam("url") String url, HttpServletRequest request, HttpServletResponse response) {
		try {

			// 构建文件路径
			File file = new File(FileUtil.adaptPath(FileUtil.getProjectFilePath() + url));

			// 检查文件是否存在
			if (!file.exists() || !file.isFile()) {
				response.setStatus(HttpServletResponse.SC_NOT_FOUND);
				response.setContentType("application/json;charset=UTF-8");
				response.getWriter().write("文件不存在");
				return;
			}

			// 获取文件名和内容类型
			String fileName = file.getName();
			String contentType = getContentType(fileName);

			// 设置响应头 - 预览模式，不设置attachment
			response.setContentType(contentType);
			response.setContentLengthLong(file.length());
			response.setHeader("Content-Disposition", "inline; filename=\"" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()) + "\"");
			response.setHeader("Cache-Control", "public, max-age=3600"); // 缓存1小时

			// 支持断点续传
			String rangeHeader = request.getHeader("Range");
			if (rangeHeader != null && rangeHeader.startsWith("bytes=")) {
				handleRangeRequest(file, rangeHeader, response);
			} else {
				// 普通预览请求
				try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));
					 OutputStream os = response.getOutputStream()) {

					byte[] buffer = new byte[8192]; // 8KB缓冲区
					int bytesRead;
					while ((bytesRead = bis.read(buffer)) != -1) {
						os.write(buffer, 0, bytesRead);
					}
					os.flush();
				}
			}

		} catch (IOException e) {
			try {
				if (!response.isCommitted()) {
					response.reset();
					response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
					response.setContentType("application/json;charset=UTF-8");
					response.getWriter().write("文件预览失败: " + e.getMessage());
				}
			} catch (IOException ex) {
				// 记录日志或处理异常
			}
		}
	}

	/**
	 * 根据文件扩展名获取Content-Type
	 * @param fileName 文件名
	 * @return Content-Type
	 */
	private String getContentType(String fileName) {
		if (!StringUtils.hasText(fileName)) {
			return MediaType.APPLICATION_OCTET_STREAM_VALUE;
		}

		String extension = FileUtil.getExtension(fileName).toLowerCase();
		switch (extension) {
			// 图片类型
			case ".jpg":
			case ".jpeg":
				return MediaType.IMAGE_JPEG_VALUE;
			case ".png":
				return MediaType.IMAGE_PNG_VALUE;
			case ".gif":
				return MediaType.IMAGE_GIF_VALUE;
			case ".bmp":
				return "image/bmp";
			case ".webp":
				return "image/webp";
			case ".svg":
				return "image/svg+xml";
			case ".ico":
				return "image/x-icon";

			// 音频类型
			case ".mp3":
				return "audio/mpeg";
			case ".wav":
				return "audio/wav";
			case ".flac":
				return "audio/flac";
			case ".aac":
				return "audio/aac";
			case ".ogg":
				return "audio/ogg";
			case ".m4a":
				return "audio/mp4";

			// 视频类型
			case ".mp4":
				return "video/mp4";
			case ".avi":
				return "video/x-msvideo";
			case ".mov":
				return "video/quicktime";
			case ".wmv":
				return "video/x-ms-wmv";
			case ".flv":
				return "video/x-flv";
			case ".webm":
				return "video/webm";

			// 文档类型
			case ".pdf":
				return MediaType.APPLICATION_PDF_VALUE;
			case ".doc":
				return "application/msword";
			case ".docx":
				return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
			case ".xls":
				return "application/vnd.ms-excel";
			case ".xlsx":
				return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
			case ".ppt":
				return "application/vnd.ms-powerpoint";
			case ".pptx":
				return "application/vnd.openxmlformats-officedocument.presentationml.presentation";

			// 文本类型
			case ".txt":
				return MediaType.TEXT_PLAIN_VALUE + ";charset=UTF-8";
			case ".html":
			case ".htm":
				return MediaType.TEXT_HTML_VALUE + ";charset=UTF-8";
			case ".css":
				return "text/css;charset=UTF-8";
			case ".js":
				return "application/javascript;charset=UTF-8";
			case ".json":
				return MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8";
			case ".xml":
				return MediaType.APPLICATION_XML_VALUE + ";charset=UTF-8";

			// 压缩文件
			case ".zip":
				return "application/zip";
			case ".rar":
				return "application/x-rar-compressed";
			case ".7z":
				return "application/x-7z-compressed";
			case ".tar":
				return "application/x-tar";
			case ".gz":
				return "application/gzip";

			default:
				return MediaType.APPLICATION_OCTET_STREAM_VALUE;
		}
	}

/**
 * 处理断点续传请求
 * @param file 文件
 * @param rangeHeader Range请求头
 * @param response HTTP响应
 * @throws IOException IO异常
 */
private void handleRangeRequest(File file, String rangeHeader, HttpServletResponse response) throws IOException {
	long fileLength = file.length();
	long start = 0;
	long end = fileLength - 1;

	// 解析Range头
	String range = rangeHeader.substring(6); // 去掉"bytes="
	if (range.contains("-")) {
		String[] ranges = range.split("-");
		try {
			if (!ranges[0].isEmpty()) {
				start = Long.parseLong(ranges[0]);
			}
			if (ranges.length > 1 && !ranges[1].isEmpty()) {
				end = Long.parseLong(ranges[1]);
			}
		} catch (NumberFormatException e) {
			// 如果解析失败，返回整个文件
			start = 0;
			end = fileLength - 1;
		}
	}

	// 确保范围有效
	if (start < 0) start = 0;
	if (end >= fileLength) end = fileLength - 1;
	if (start > end) {
		response.setStatus(HttpServletResponse.SC_REQUESTED_RANGE_NOT_SATISFIABLE);
		response.setHeader("Content-Range", "bytes */" + fileLength);
		return;
	}

	long contentLength = end - start + 1;

	// 设置206状态码和相关头
	response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
	response.setHeader("Content-Range", "bytes " + start + "-" + end + "/" + fileLength);
	response.setHeader("Accept-Ranges", "bytes");
	response.setContentLengthLong(contentLength);

	// 传输指定范围的文件内容
	try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));
		 OutputStream os = response.getOutputStream()) {

		// 跳过开始位置之前的字节
		bis.skip(start);

		byte[] buffer = new byte[8192];
		long remaining = contentLength;
		int bytesRead;

		while (remaining > 0 && (bytesRead = bis.read(buffer, 0, (int) Math.min(buffer.length, remaining))) != -1) {
			os.write(buffer, 0, bytesRead);
			remaining -= bytesRead;
		}
		os.flush();
	}

}
}
